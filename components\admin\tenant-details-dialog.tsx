"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Building2, 
  Users, 
  ShoppingCart, 
  FileText, 
  CreditCard,
  Calendar,
  Globe,
  CheckCircle,
  AlertTriangle,
  XCircle,
  TrendingUp,
  Database,
  Mail
} from 'lucide-react'

interface Tenant {
  id: string
  name: string
  slug: string
  domain?: string
  logo?: string
  isActive: boolean
  plan: string
  maxUsers?: number
  maxStorage?: number
  subscriptionStatus: string
  billingEmail?: string
  createdAt: string
  updatedAt: string
  _count: {
    users: number
    customers: number
    orders: number
  }
}

interface TenantStats {
  tenant: {
    id: string
    name: string
    slug: string
  }
  overview: {
    totalUsers: number
    activeUsers: number
    totalCustomers: number
    totalSuppliers: number
    totalProducts: number
    totalOrders: number
    totalInvoices: number
    totalPayments: number
  }
  recentActivity: {
    newUsers: number
    newOrders: number
    newInvoices: number
  }
  financial: {
    totalOrderValue: number
    averageOrderValue: number
    totalInvoiceValue: number
    averageInvoiceValue: number
    totalPayments: number
    averagePayment: number
  }
  breakdowns: {
    orderStatus: Array<{ status: string; count: number }>
    invoiceStatus: Array<{ status: string; count: number }>
  }
}

interface TenantDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tenant: Tenant
}

export function TenantDetailsDialog({ open, onOpenChange, tenant }: TenantDetailsDialogProps) {
  const [stats, setStats] = useState<TenantStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open && tenant) {
      fetchTenantStats()
    }
  }, [open, tenant])

  const fetchTenantStats = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/tenants/${tenant.id}/stats`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching tenant stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {tenant?.name}
          </DialogTitle>
          <DialogDescription>
            Detailed information and statistics for this tenant organization.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Name</span>
                    <p className="text-sm">{tenant?.name}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Slug</span>
                    <p className="text-sm font-mono">{tenant?.slug}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Domain</span>
                    <p className="text-sm">{tenant?.domain || 'Not set'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Status</span>
                    <div className="flex items-center gap-2">
                      <Badge variant={tenant?.isActive ? "default" : "secondary"}>
                        {tenant?.isActive ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Created</span>
                    <p className="text-sm">{tenant ? new Date(tenant.createdAt).toLocaleDateString() : ''}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Last Updated</span>
                    <p className="text-sm">{tenant ? new Date(tenant.updatedAt).toLocaleDateString() : ''}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold">{tenant?._count.users || 0}</p>
                      <p className="text-xs text-muted-foreground">Users</p>
                    </div>
                    <Users className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold">{tenant?._count.customers || 0}</p>
                      <p className="text-xs text-muted-foreground">Customers</p>
                    </div>
                    <FileText className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold">{tenant?._count.orders || 0}</p>
                      <p className="text-xs text-muted-foreground">Orders</p>
                    </div>
                    <ShoppingCart className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading statistics...</div>
              </div>
            ) : stats ? (
              <>
                {/* Overview Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{stats.overview.totalUsers}</p>
                        <p className="text-xs text-muted-foreground">Total Users</p>
                        <p className="text-xs text-green-600">{stats.overview.activeUsers} active</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{stats.overview.totalProducts}</p>
                        <p className="text-xs text-muted-foreground">Products</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{stats.overview.totalOrders}</p>
                        <p className="text-xs text-muted-foreground">Orders</p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{stats.overview.totalInvoices}</p>
                        <p className="text-xs text-muted-foreground">Invoices</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Financial Stats */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Financial Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Order Value</p>
                        <p className="text-2xl font-bold">{formatCurrency(stats.financial.totalOrderValue)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Average Order Value</p>
                        <p className="text-2xl font-bold">{formatCurrency(stats.financial.averageOrderValue)}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Payments</p>
                        <p className="text-2xl font-bold">{formatCurrency(stats.financial.totalPayments)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Recent Activity (Last 7 Days)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{stats.recentActivity.newUsers}</p>
                        <p className="text-xs text-muted-foreground">New Users</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{stats.recentActivity.newOrders}</p>
                        <p className="text-xs text-muted-foreground">New Orders</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">{stats.recentActivity.newInvoices}</p>
                        <p className="text-xs text-muted-foreground">New Invoices</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">No statistics available</div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {/* Subscription & Plan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Subscription & Plan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Plan</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">{tenant?.plan}</Badge>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Subscription Status</span>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(tenant?.subscriptionStatus || '')}>
                        {getStatusIcon(tenant?.subscriptionStatus || '')}
                        <span className="ml-1 capitalize">{tenant?.subscriptionStatus}</span>
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Max Users</span>
                    <p className="text-sm">{tenant?.maxUsers || 'Unlimited'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Max Storage</span>
                    <p className="text-sm">{tenant?.maxStorage ? `${tenant.maxStorage} MB` : 'Unlimited'}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Billing Email</span>
                    <p className="text-sm">{tenant?.billingEmail || 'Not set'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Technical Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Technical Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Tenant ID</span>
                    <p className="text-sm font-mono">{tenant?.id}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Database Isolation</span>
                    <p className="text-sm text-green-600">✓ Enabled</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
