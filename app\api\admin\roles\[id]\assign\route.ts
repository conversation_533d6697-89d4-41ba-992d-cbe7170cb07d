import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const assignRoleSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user must be selected'),
  action: z.enum(['assign', 'revoke']),
})

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!(session as any)?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { userIds, action } = assignRoleSchema.parse(body)

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: params.id },
    })

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 })
    }

    // Verify all users exist and belong to the same tenant as the role
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
        tenantId: role.tenantId,
      },
    })

    if (users.length !== userIds.length) {
      return NextResponse.json(
        { error: 'Some users not found or do not belong to the role tenant' },
        { status: 400 }
      )
    }

    if (action === 'assign') {
      // Assign role to users (ignore if already assigned)
      const assignments = userIds.map(userId => ({
        userId,
        roleId: params.id,
      }))

      await prisma.userRole.createMany({
        data: assignments,
        skipDuplicates: true,
      })

      return NextResponse.json({ 
        message: `Role assigned to ${userIds.length} user(s) successfully` 
      })
    } else {
      // Revoke role from users
      await prisma.userRole.deleteMany({
        where: {
          userId: { in: userIds },
          roleId: params.id,
        },
      })

      return NextResponse.json({ 
        message: `Role revoked from ${userIds.length} user(s) successfully` 
      })
    }
  } catch (error) {
    console.error('Error assigning/revoking role:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
