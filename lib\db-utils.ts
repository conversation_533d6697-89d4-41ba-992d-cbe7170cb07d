import { prisma } from './prisma'
import { createTenantPrisma } from './tenant'

/**
 * Get tenant-scoped database client
 */
export function getTenantDb(tenantId: string) {
  return createTenantPrisma(tenantId)
}

/**
 * Generate next order number for a tenant
 */
export async function generateOrderNumber(tenantId: string): Promise<string> {
  const lastOrder = await prisma.order.findFirst({
    where: { tenantId },
    orderBy: { createdAt: 'desc' },
    select: { orderNumber: true }
  })

  if (!lastOrder) {
    return 'ORD-0001'
  }

  // Extract number from last order (assuming format ORD-XXXX)
  const lastNumber = parseInt(lastOrder.orderNumber.split('-')[1] || '0')
  const nextNumber = lastNumber + 1
  return `ORD-${nextNumber.toString().padStart(4, '0')}`
}

/**
 * Generate next invoice number for a tenant
 */
export async function generateInvoiceNumber(tenantId: string): Promise<string> {
  const lastInvoice = await prisma.invoice.findFirst({
    where: { tenantId },
    orderBy: { createdAt: 'desc' },
    select: { invoiceNumber: true }
  })

  if (!lastInvoice) {
    return 'INV-0001'
  }

  // Extract number from last invoice (assuming format INV-XXXX)
  const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-')[1] || '0')
  const nextNumber = lastNumber + 1
  return `INV-${nextNumber.toString().padStart(4, '0')}`
}

/**
 * Calculate order totals
 */
export function calculateOrderTotals(items: Array<{ quantity: number; unitPrice: number }>, taxRate: number = 0) {
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
  const taxAmount = subtotal * taxRate
  const totalAmount = subtotal + taxAmount

  return {
    subtotal: Number(subtotal.toFixed(2)),
    taxAmount: Number(taxAmount.toFixed(2)),
    totalAmount: Number(totalAmount.toFixed(2))
  }
}

/**
 * Update inventory when order is created/updated
 */
export async function updateInventoryForOrder(
  tenantId: string,
  orderItems: Array<{ productId: string; quantity: number }>,
  operation: 'add' | 'subtract' = 'subtract'
) {
  for (const item of orderItems) {
    const inventory = await prisma.inventoryItem.findFirst({
      where: {
        tenantId,
        productId: item.productId
      }
    })

    if (inventory) {
      const newQuantity = operation === 'subtract' 
        ? inventory.quantity - item.quantity
        : inventory.quantity + item.quantity

      await prisma.inventoryItem.update({
        where: { id: inventory.id },
        data: { 
          quantity: Math.max(0, newQuantity), // Prevent negative inventory
          lastUpdated: new Date()
        }
      })
    }
  }
}

/**
 * Get low stock products for a tenant
 */
export async function getLowStockProducts(tenantId: string) {
  return await prisma.product.findMany({
    where: {
      tenantId,
      isActive: true,
      minStock: { not: null }
    },
    include: {
      inventory: true
    }
  }).then(products => 
    products.filter(product => {
      const totalStock = product.inventory.reduce((sum, inv) => sum + inv.quantity, 0)
      return product.minStock && totalStock <= product.minStock
    })
  )
}

/**
 * Get dashboard statistics for a tenant
 */
export async function getDashboardStats(tenantId: string) {
  const [
    totalCustomers,
    totalProducts,
    totalOrders,
    totalInvoices,
    pendingOrders,
    overdueInvoices,
    lowStockProducts
  ] = await Promise.all([
    prisma.customer.count({ where: { tenantId, isActive: true } }),
    prisma.product.count({ where: { tenantId, isActive: true } }),
    prisma.order.count({ where: { tenantId } }),
    prisma.invoice.count({ where: { tenantId } }),
    prisma.order.count({ where: { tenantId, status: 'PENDING' } }),
    prisma.invoice.count({ 
      where: { 
        tenantId, 
        status: 'SENT',
        dueDate: { lt: new Date() }
      } 
    }),
    getLowStockProducts(tenantId)
  ])

  return {
    totalCustomers,
    totalProducts,
    totalOrders,
    totalInvoices,
    pendingOrders,
    overdueInvoices,
    lowStockCount: lowStockProducts.length
  }
}

/**
 * Search across multiple entities for a tenant
 */
export async function globalSearch(tenantId: string, query: string) {
  const searchTerm = `%${query}%`

  const [customers, products, orders, invoices] = await Promise.all([
    prisma.customer.findMany({
      where: {
        tenantId,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { email: { contains: query, mode: 'insensitive' } }
        ]
      },
      take: 5
    }),
    prisma.product.findMany({
      where: {
        tenantId,
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { sku: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      },
      take: 5
    }),
    prisma.order.findMany({
      where: {
        tenantId,
        orderNumber: { contains: query, mode: 'insensitive' }
      },
      include: { customer: true },
      take: 5
    }),
    prisma.invoice.findMany({
      where: {
        tenantId,
        invoiceNumber: { contains: query, mode: 'insensitive' }
      },
      include: { customer: true },
      take: 5
    })
  ])

  return {
    customers,
    products,
    orders,
    invoices
  }
}
