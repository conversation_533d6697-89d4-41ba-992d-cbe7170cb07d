# Clover ERP - Multi-Tenant Enterprise Resource Planning System

A modern, multi-tenant ERP system built with Next.js 15, Prisma, and NextAuth. This application supports multiple organizations with complete data isolation and role-based access control.

## 🚀 Features

### Multi-Tenancy
- **Complete Data Isolation**: Each tenant's data is completely separated
- **Tenant-Aware Authentication**: Users belong to specific tenants
- **Flexible Tenant Resolution**: Support for slug-based and domain-based tenant identification
- **Scalable Architecture**: Shared database with tenant-scoped queries

### Authentication & Authorization
- **NextAuth Integration**: Secure authentication with multiple providers
- **Role-Based Access Control (RBAC)**: Granular permissions system
- **Tenant-Scoped Users**: Users can only access their organization's data
- **Password Hashing**: Secure bcrypt password storage

### ERP Modules
- **Customer Management**: Complete customer lifecycle management
- **Product Catalog**: Product management with categories and suppliers
- **Order Management**: Full order processing workflow
- **Invoice Management**: Automated invoicing with payment tracking
- **Inventory Management**: Stock tracking with low-stock alerts
- **Dashboard Analytics**: Real-time business insights

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL with Prisma
- **Authentication**: NextAuth.js v4
- **UI Components**: shadcn/ui (Radix UI + Tailwind)
- **Validation**: Zod schema validation
- **Charts**: Recharts for data visualization

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd clover.erp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your database URL and other configuration:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/clover_erp"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   DEFAULT_TENANT_SLUG="default"
   ```

4. **Set up the database**
   ```bash
   # Push the schema to your database
   npm run db:push

   # Seed the database with initial data
   npm run db:seed
   ```

5. **Generate Prisma client**
   ```bash
   npm run db:generate
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

## 🔐 Default Login Credentials

After running the seed script, you can log in with:

- **Email**: <EMAIL>
- **Password**: admin123
- **Organization**: default

## 🏗️ Database Schema

The application uses a shared database with tenant isolation through foreign keys:

### Core Tables
- `tenants` - Organization/tenant information
- `users` - User accounts (tenant-scoped)
- `roles` - Role definitions (tenant-scoped)
- `permissions` - Permission definitions (tenant-scoped)
- `user_roles` - User-role assignments
- `role_permissions` - Role-permission assignments

### ERP Tables
- `customers` - Customer management
- `suppliers` - Supplier management
- `categories` - Product categories
- `products` - Product catalog
- `orders` - Order management
- `order_items` - Order line items
- `invoices` - Invoice management
- `invoice_items` - Invoice line items
- `payments` - Payment tracking
- `inventory_items` - Inventory management

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with initial data
- `npm run db:studio` - Open Prisma Studio
- `npm run db:reset` - Reset database

## 🏢 Multi-Tenant Architecture

### Tenant Isolation Strategy
- **Shared Database**: Single database with tenant_id foreign keys
- **Row-Level Security**: All queries are automatically scoped to the current tenant
- **Tenant-Aware APIs**: All API endpoints validate tenant access
- **Secure Authentication**: Users can only access their tenant's data

### Creating New Tenants
```typescript
import { createTenant } from '@/lib/tenant'

const newTenant = await createTenant({
  name: 'Acme Corporation',
  slug: 'acme-corp',
  domain: 'acme.example.com', // optional
  adminEmail: '<EMAIL>',
  adminName: 'John Doe',
  adminPassword: 'secure-password'
})
```

## 🔒 Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Sessions**: Secure session management
- **CSRF Protection**: Built-in CSRF protection
- **Input Validation**: Zod schema validation on all inputs
- **SQL Injection Prevention**: Prisma ORM with parameterized queries
- **Tenant Isolation**: Complete data separation between tenants

## 📊 API Endpoints

### Authentication
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `POST /api/auth/register` - User registration

### Dashboard
- `GET /api/dashboard/stats` - Dashboard statistics

### Customers
- `GET /api/customers` - List customers (tenant-scoped)
- `POST /api/customers` - Create customer
- `PUT /api/customers/[id]` - Update customer
- `DELETE /api/customers/[id]` - Delete customer

## 🚀 Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set up production database**
   - Create a PostgreSQL database
   - Update `DATABASE_URL` in production environment
   - Run migrations: `npm run db:migrate`

3. **Deploy to your platform**
   - Vercel, Netlify, or any Node.js hosting platform
   - Ensure environment variables are set
   - Run the seed script for initial data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the code examples in the `/lib` directory
