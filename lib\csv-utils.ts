import { User, Tenant } from '@prisma/client'

export interface UserWithTenant extends User {
  tenant: Tenant | null
}

export function parseCSV(csvContent: string): any[] {
  const lines = csvContent.trim().split('\n')
  if (lines.length < 2) {
    throw new Error('CSV must contain at least a header row and one data row')
  }

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  const data = []

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
    if (values.length !== headers.length) {
      throw new Error(`Row ${i + 1} has ${values.length} columns, expected ${headers.length}`)
    }

    const row: any = {}
    headers.forEach((header, index) => {
      row[header] = values[index]
    })
    data.push(row)
  }

  return data
}

export function validateUserCSVData(data: any[]): any[] {
  const requiredFields = ['name', 'email', 'password', 'tenantId']
  
  return data.map((row, index) => {
    const missingFields = requiredFields.filter(field => !row[field])
    if (missingFields.length > 0) {
      throw new Error(`Row ${index + 2} is missing required fields: ${missingFields.join(', ')}`)
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(row.email)) {
      throw new Error(`Row ${index + 2} has invalid email format: ${row.email}`)
    }

    // Validate password length
    if (row.password.length < 6) {
      throw new Error(`Row ${index + 2} has password shorter than 6 characters`)
    }

    return {
      name: row.name,
      email: row.email.toLowerCase(),
      password: row.password,
      tenantId: row.tenantId,
      isActive: row.isActive === 'true' || row.isActive === '1' || row.isActive === true,
    }
  })
}

export function generateUserCSV(users: UserWithTenant[]): string {
  const headers = ['id', 'name', 'email', 'tenantId', 'tenantName', 'isActive', 'isSuperAdmin', 'lastLoginAt', 'createdAt']
  
  const csvRows = [
    headers.join(','),
    ...users.map(user => [
      user.id,
      `"${user.name || ''}"`,
      user.email,
      user.tenantId || '',
      `"${user.tenant?.name || ''}"`,
      user.isActive,
      user.isSuperAdmin,
      user.lastLoginAt?.toISOString() || '',
      user.createdAt.toISOString(),
    ].join(','))
  ]

  return csvRows.join('\n')
}

export function downloadCSV(content: string, filename: string) {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
