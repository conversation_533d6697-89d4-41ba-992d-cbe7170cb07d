"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Shield, Key } from 'lucide-react'

const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(50, 'Role name must be less than 50 characters'),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
})

type UpdateRoleInput = z.infer<typeof updateRoleSchema>

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
}

interface Role {
  id: string
  name: string
  description?: string
  isSystem: boolean
  tenant: {
    id: string
    name: string
    slug: string
  }
  rolePermissions?: {
    permission: Permission
  }[]
}

interface EditRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: Role
  onSuccess: () => void
}

export function EditRoleDialog({ open, onOpenChange, role, onSuccess }: EditRoleDialogProps) {
  const [loading, setLoading] = useState(false)
  const [permissions, setPermissions] = useState<Permission[]>([])
  const { toast } = useToast()

  const form = useForm<UpdateRoleInput>({
    resolver: zodResolver(updateRoleSchema),
    defaultValues: {
      name: role.name,
      description: role.description || '',
      permissions: role.rolePermissions?.map(rp => rp.permission.id) || [],
    },
  })

  const fetchPermissions = async () => {
    try {
      const response = await fetch(`/api/admin/permissions?tenantId=${role.tenant.id}&limit=1000`)
      if (!response.ok) {
        throw new Error('Failed to fetch permissions')
      }
      const data = await response.json()
      setPermissions(data.permissions || [])
    } catch (error) {
      console.error('Error fetching permissions:', error)
      toast({
        title: "Error",
        description: "Failed to fetch permissions",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    if (open) {
      fetchPermissions()
      form.reset({
        name: role.name,
        description: role.description || '',
        permissions: role.rolePermissions?.map(rp => rp.permission.id) || [],
      })
    }
  }, [open, role, form])

  const onSubmit = async (data: UpdateRoleInput) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/roles/${role.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update role')
      }

      onSuccess()
    } catch (error: any) {
      console.error('Error updating role:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to update role",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = []
    }
    acc[permission.resource].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  const selectedPermissions = form.watch('permissions') || []

  const togglePermission = (permissionId: string) => {
    const current = selectedPermissions
    const updated = current.includes(permissionId)
      ? current.filter(id => id !== permissionId)
      : [...current, permissionId]
    form.setValue('permissions', updated)
  }

  const toggleAllResourcePermissions = (resource: string) => {
    const resourcePermissions = groupedPermissions[resource] || []
    const resourcePermissionIds = resourcePermissions.map(p => p.id)
    const current = selectedPermissions
    const allSelected = resourcePermissionIds.every(id => current.includes(id))
    
    if (allSelected) {
      // Remove all resource permissions
      const updated = current.filter(id => !resourcePermissionIds.includes(id))
      form.setValue('permissions', updated)
    } else {
      // Add all resource permissions
      const updated = [...new Set([...current, ...resourcePermissionIds])]
      form.setValue('permissions', updated)
    }
  }

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'read': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'update': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'delete': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'manage': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Edit Role: {role.name}
            {role.isSystem && (
              <Badge variant="default" className="ml-2">System</Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Update role details and manage permissions.
            {role.isSystem && " Note: System roles have restricted editing capabilities."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role Name</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter role name" 
                        disabled={role.isSystem}
                        {...field} 
                      />
                    </FormControl>
                    {role.isSystem && (
                      <FormDescription>
                        System role names cannot be changed.
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <label className="text-sm font-medium">Tenant</label>
                <div className="p-3 border rounded-md bg-muted">
                  <div className="font-medium">{role.tenant.name}</div>
                  <div className="text-sm text-muted-foreground">{role.tenant.slug}</div>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Enter role description (optional)" 
                      className="resize-none" 
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {permissions.length > 0 && (
              <FormField
                control={form.control}
                name="permissions"
                render={() => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Key className="h-4 w-4" />
                      Permissions ({selectedPermissions.length} selected)
                    </FormLabel>
                    <FormDescription>
                      Select the permissions this role should have. Permissions are grouped by resource.
                    </FormDescription>
                    <ScrollArea className="h-[300px] border rounded-md p-4">
                      <div className="space-y-4">
                        {Object.entries(groupedPermissions).map(([resource, resourcePermissions]) => {
                          const resourcePermissionIds = resourcePermissions.map(p => p.id)
                          const allSelected = resourcePermissionIds.every(id => selectedPermissions.includes(id))
                          const someSelected = resourcePermissionIds.some(id => selectedPermissions.includes(id))

                          return (
                            <div key={resource} className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  checked={allSelected}
                                  ref={(el) => {
                                    if (el) el.indeterminate = someSelected && !allSelected
                                  }}
                                  onCheckedChange={() => toggleAllResourcePermissions(resource)}
                                />
                                <Badge variant="outline" className="capitalize font-medium">
                                  {resource}
                                </Badge>
                                <span className="text-sm text-muted-foreground">
                                  ({resourcePermissions.length} permissions)
                                </span>
                              </div>
                              <div className="ml-6 grid grid-cols-2 gap-2">
                                {resourcePermissions.map((permission) => (
                                  <div key={permission.id} className="flex items-center space-x-2">
                                    <Checkbox
                                      checked={selectedPermissions.includes(permission.id)}
                                      onCheckedChange={() => togglePermission(permission.id)}
                                    />
                                    <div className="flex items-center gap-2">
                                      <Badge 
                                        variant="secondary" 
                                        className={`text-xs ${getActionColor(permission.action)}`}
                                      >
                                        {permission.action}
                                      </Badge>
                                      <span className="text-sm">{permission.name}</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                              {resource !== Object.keys(groupedPermissions)[Object.keys(groupedPermissions).length - 1] && (
                                <Separator className="mt-4" />
                              )}
                            </div>
                          )
                        })}
                      </div>
                    </ScrollArea>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Role
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
