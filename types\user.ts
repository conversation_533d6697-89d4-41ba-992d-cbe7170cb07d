export interface User {
  id: string
  name: string | null
  email: string
  isActive: boolean
  isSuperAdmin: boolean
  lastLoginAt: string | null
  createdAt: string
  updatedAt: string
  tenant: {
    id: string
    name: string
    slug: string
  } | null
}

export interface UserWithTenant extends User {
  tenant: {
    id: string
    name: string
    slug: string
  }
}

export interface UsersResponse {
  users: User[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface Tenant {
  id: string
  name: string
  slug: string
  domain?: string | null
  logo?: string | null
  isActive: boolean
  maxUsers?: number | null
  maxStorage?: number | null
  plan: string
  createdAt: string
  updatedAt: string
}

export type UserStatus = 'active' | 'inactive' | 'pending'

export interface UserFilters {
  search?: string
  tenantId?: string
  status?: UserStatus | ''
  page?: number
  limit?: number
}
