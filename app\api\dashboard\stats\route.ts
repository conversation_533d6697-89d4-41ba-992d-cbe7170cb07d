import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { getDashboardStats } from '@/lib/db-utils'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.tenantId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const stats = await getDashboardStats(session.tenantId)
    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
