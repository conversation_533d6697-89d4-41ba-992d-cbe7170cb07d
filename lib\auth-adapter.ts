import { Adapter } from "next-auth/adapters"
import { prisma } from "./prisma"
import { getTenantBySlug, getTenantByDomain } from "./tenant"

export interface MultiTenantAdapterOptions {
  getTenantId?: (email: string, request?: any) => Promise<string | null>
}

export function MultiTenantPrismaAdapter(
  options: MultiTenantAdapterOptions = {}
): Adapter {
  return {
    async createUser(user) {
      // Extract tenant information from email domain or other logic
      let tenantId: string | null = null
      
      if (options.getTenantId) {
        tenantId = await options.getTenantId(user.email!)
      }
      
      if (!tenantId) {
        // Default tenant resolution logic
        // You can customize this based on your needs
        const emailDomain = user.email!.split('@')[1]
        const tenant = await getTenantByDomain(emailDomain)
        tenantId = tenant?.id || null
      }
      
      if (!tenantId) {
        throw new Error('Unable to determine tenant for user')
      }

      const createdUser = await prisma.user.create({
        data: {
          tenantId,
          name: user.name,
          email: user.email!,
          emailVerified: user.emailVerified,
          image: user.image,
        },
      })

      return {
        id: createdUser.id,
        name: createdUser.name,
        email: createdUser.email!,
        emailVerified: createdUser.emailVerified,
        image: createdUser.image,
      }
    },

    async getUser(id) {
      const user = await prisma.user.findUnique({
        where: { id },
        include: { tenant: true }
      })

      if (!user) return null

      return {
        id: user.id,
        name: user.name,
        email: user.email!,
        emailVerified: user.emailVerified,
        image: user.image,
      }
    },

    async getUserByEmail(email) {
      // Note: This might return multiple users from different tenants
      // You may need additional logic to determine the correct tenant
      const user = await prisma.user.findFirst({
        where: { email },
        include: { tenant: true }
      })

      if (!user) return null

      return {
        id: user.id,
        name: user.name,
        email: user.email!,
        emailVerified: user.emailVerified,
        image: user.image,
      }
    },

    async getUserByAccount({ providerAccountId, provider }) {
      const account = await prisma.account.findUnique({
        where: { provider_providerAccountId: { provider, providerAccountId } },
        select: { user: true },
      })

      if (!account?.user) return null

      return {
        id: account.user.id,
        name: account.user.name,
        email: account.user.email!,
        emailVerified: account.user.emailVerified,
        image: account.user.image,
      }
    },

    async updateUser({ id, ...data }) {
      const user = await prisma.user.update({
        where: { id },
        data,
      })

      return {
        id: user.id,
        name: user.name,
        email: user.email!,
        emailVerified: user.emailVerified,
        image: user.image,
      }
    },

    async deleteUser(userId) {
      await prisma.user.delete({ where: { id: userId } })
    },

    async linkAccount(account) {
      await prisma.account.create({ data: account })
    },

    async unlinkAccount({ providerAccountId, provider }) {
      await prisma.account.delete({
        where: { provider_providerAccountId: { provider, providerAccountId } },
      })
    },

    async createSession({ sessionToken, userId, expires }) {
      const session = await prisma.session.create({
        data: { sessionToken, userId, expires },
      })

      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      }
    },

    async getSessionAndUser(sessionToken) {
      const userAndSession = await prisma.session.findUnique({
        where: { sessionToken },
        include: { 
          user: {
            include: {
              tenant: true,
              userRoles: {
                include: {
                  role: {
                    include: {
                      rolePermissions: {
                        include: {
                          permission: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
      })

      if (!userAndSession) return null

      const { user, ...session } = userAndSession

      return {
        session: {
          sessionToken: session.sessionToken,
          userId: session.userId,
          expires: session.expires,
        },
        user: {
          id: user.id,
          name: user.name,
          email: user.email!,
          emailVerified: user.emailVerified,
          image: user.image,
        },
      }
    },

    async updateSession({ sessionToken, ...data }) {
      const session = await prisma.session.update({
        where: { sessionToken },
        data,
      })

      return {
        sessionToken: session.sessionToken,
        userId: session.userId,
        expires: session.expires,
      }
    },

    async deleteSession(sessionToken) {
      await prisma.session.delete({ where: { sessionToken } })
    },

    async createVerificationToken({ identifier, expires, token }) {
      const verificationToken = await prisma.verificationToken.create({
        data: { identifier, expires, token },
      })

      return {
        identifier: verificationToken.identifier,
        expires: verificationToken.expires,
        token: verificationToken.token,
      }
    },

    async useVerificationToken({ identifier, token }) {
      try {
        const verificationToken = await prisma.verificationToken.delete({
          where: { identifier_token: { identifier, token } },
        })

        return {
          identifier: verificationToken.identifier,
          expires: verificationToken.expires,
          token: verificationToken.token,
        }
      } catch (error) {
        return null
      }
    },
  }
}
