"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Key, 
  Shield, 
  Calendar,
  Building2,
  Users
} from 'lucide-react'

interface Role {
  id: string
  name: string
  description?: string
  isSystem: boolean
}

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
  createdAt: string
  tenant: {
    id: string
    name: string
    slug: string
  }
  _count: {
    rolePermissions: number
  }
}

interface PermissionDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  permission: Permission
}

interface PermissionDetails {
  permission: Permission & {
    rolePermissions: {
      role: Role
    }[]
  }
}

export function PermissionDetailsDialog({ open, onOpenChange, permission }: PermissionDetailsDialogProps) {
  const [loading, setLoading] = useState(true)
  const [permissionDetails, setPermissionDetails] = useState<PermissionDetails | null>(null)

  const fetchPermissionDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/permissions/${permission.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch permission details')
      }
      const data = await response.json()
      setPermissionDetails(data)
    } catch (error) {
      console.error('Error fetching permission details:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchPermissionDetails()
    }
  }, [open, permission.id])

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'read': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'update': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'delete': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'manage': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Permission Details: {permission.name}
          </DialogTitle>
          <DialogDescription>
            Detailed information about the permission and its role assignments.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Permission Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Permission Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <div className="font-medium">{permission.name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Resource</label>
                  <div>
                    <Badge variant="outline" className="capitalize">
                      {permission.resource}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Action</label>
                  <div>
                    <Badge 
                      variant="secondary" 
                      className={`capitalize ${getActionColor(permission.action)}`}
                    >
                      {permission.action}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Assigned Roles</label>
                  <div className="font-medium">{permission._count.rolePermissions}</div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Description</label>
                <div className="text-sm">
                  {permission.description || 'No description provided'}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Building2 className="h-3 w-3" />
                    Tenant
                  </label>
                  <div>
                    <div className="font-medium">{permission.tenant.name}</div>
                    <div className="text-sm text-muted-foreground">{permission.tenant.slug}</div>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Created
                  </label>
                  <div className="text-sm">
                    {new Date(permission.createdAt).toLocaleDateString()} at{' '}
                    {new Date(permission.createdAt).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assigned Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Assigned Roles ({permission._count.rolePermissions})
              </CardTitle>
              <CardDescription>
                Roles that have been granted this permission.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-[150px]" />
                        <Skeleton className="h-3 w-[120px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : !permissionDetails?.permission.rolePermissions.length ? (
                <div className="text-center py-8 text-muted-foreground">
                  No roles have been assigned this permission
                </div>
              ) : (
                <ScrollArea className="h-[200px]">
                  <div className="space-y-3">
                    {permissionDetails.permission.rolePermissions.map(({ role }) => (
                      <div key={role.id} className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                          <Shield className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {role.name}
                            <Badge variant={role.isSystem ? "default" : "secondary"} className="text-xs">
                              {role.isSystem ? 'System' : 'Custom'}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {role.description || 'No description'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {/* Permission Usage Examples */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Usage Examples
              </CardTitle>
              <CardDescription>
                How this permission can be used in the application.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-muted rounded-md">
                  <div className="font-medium text-sm">API Check</div>
                  <code className="text-xs text-muted-foreground">
                    hasPermission('{permission.name}')
                  </code>
                </div>
                <div className="p-3 bg-muted rounded-md">
                  <div className="font-medium text-sm">Resource Access</div>
                  <div className="text-xs text-muted-foreground">
                    Allows {permission.action} operations on {permission.resource} resources
                  </div>
                </div>
                {permission.description && (
                  <div className="p-3 bg-muted rounded-md">
                    <div className="font-medium text-sm">Description</div>
                    <div className="text-xs text-muted-foreground">
                      {permission.description}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
