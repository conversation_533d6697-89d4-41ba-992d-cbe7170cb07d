import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createPermissionSchema = z.object({
  name: z.string().min(1).max(100),
  resource: z.string().min(1),
  action: z.string().min(1),
  description: z.string().optional(),
  tenantId: z.string(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const resource = searchParams.get('resource') || 'all'
    const action = searchParams.get('action') || 'all'
    const tenantId = searchParams.get('tenantId')

    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { resource: { contains: search, mode: 'insensitive' } },
        { action: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (resource !== 'all') {
      where.resource = resource
    }

    if (action !== 'all') {
      where.action = action
    }

    if (tenantId && tenantId !== 'all') {
      where.tenantId = tenantId
    }

    const [permissions, total] = await Promise.all([
      prisma.permission.findMany({
        where,
        include: {
          tenant: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              rolePermissions: true,
            },
          },
        },
        orderBy: [
          { resource: 'asc' },
          { action: 'asc' },
          { name: 'asc' },
        ],
        skip,
        take: limit,
      }),
      prisma.permission.count({ where }),
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      permissions,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    })
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, resource, action, description, tenantId } = createPermissionSchema.parse(body)

    // Check if permission name already exists in the tenant
    const existingPermission = await prisma.permission.findUnique({
      where: {
        tenantId_name: {
          tenantId,
          name,
        },
      },
    })

    if (existingPermission) {
      return NextResponse.json(
        { error: 'Permission name already exists in this tenant' },
        { status: 400 }
      )
    }

    // Verify tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
    })

    if (!tenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        name,
        resource,
        action,
        description,
        tenantId,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            rolePermissions: true,
          },
        },
      },
    })

    return NextResponse.json(permission, { status: 201 })
  } catch (error) {
    console.error('Error creating permission:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
