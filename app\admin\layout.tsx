import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { redirect } from 'next/navigation'
import { AdminSidebar } from '@/components/admin/admin-sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'

async function checkSuperAdmin(session: any) {
  if (!session?.user?.id) {
    return false
  }

  // First check if the session already contains isSuperAdmin info
  if (typeof (session as any).isSuperAdmin === 'boolean') {
    return (session as any).isSuperAdmin
  }

  // Fallback to database query if not in session
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { isSuperAdmin: true }
  })

  return user?.isSuperAdmin || false
}

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth()

  if (!session) {
    redirect('/signin')
  }

  const isSuperAdmin = await checkSuperAdmin(session)

  if (!isSuperAdmin) {
    redirect('/dashboard')
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <AdminSidebar />
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  )
}
