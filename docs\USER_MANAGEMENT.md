# User Management Feature

## Overview

The User Management feature provides comprehensive CRUD operations for managing users across all tenant organizations in the Clover ERP system. This feature is accessible only to Super Admin users and includes advanced functionality like CSV import/export, password reset, and user status management.

## Features

### ✅ Implemented Features

1. **User CRUD Operations**
   - Create new users with tenant assignment
   - View user details and information
   - Edit user information (name, email, tenant, status, role)
   - Delete users (with protection against self-deletion)

2. **User Status Management**
   - Active/Inactive status toggle
   - Visual status indicators with badges
   - Filter users by status

3. **Import/Export Functionality**
   - CSV import with validation and error handling
   - CSV export with filtering options
   - Template download for proper format

4. **Password Management**
   - Password reset functionality for any user
   - Random password generation
   - Secure password hashing with bcrypt

5. **Advanced Features**
   - Search functionality across name and email
   - Pagination for large user lists
   - Tenant filtering
   - Super Admin role management
   - Multi-tenant data isolation

## API Endpoints

### User Management
- `GET /api/admin/users` - List users with pagination and filtering
- `POST /api/admin/users` - Create new user
- `GET /api/admin/users/[id]` - Get user details
- `PUT /api/admin/users/[id]` - Update user
- `DELETE /api/admin/users/[id]` - Delete user

### Password Management
- `POST /api/admin/users/[id]/reset-password` - Reset user password

### Import/Export
- `POST /api/admin/users/import` - Import users from CSV
- `GET /api/admin/users/export` - Export users to CSV

## User Interface

### Main User Management Page
- **Location**: `/admin/users`
- **Access**: Super Admin only
- **Features**:
  - Searchable and filterable user table
  - Bulk actions (export, import)
  - Individual user actions (edit, delete, reset password)
  - Pagination controls
  - Status badges and role indicators

### Dialogs and Forms
1. **Create User Dialog** - Form to add new users
2. **Edit User Dialog** - Form to update user information
3. **Password Reset Dialog** - Interface to reset user passwords
4. **Import Users Dialog** - CSV upload with template download
5. **User Details Dialog** - Read-only view of user information

## CSV Import/Export Format

### Import CSV Format
```csv
name,email,password,tenantId,isActive
John Doe,<EMAIL>,password123,tenant-id-1,true
Jane Smith,<EMAIL>,password456,tenant-id-2,true
```

### Export CSV Format
```csv
id,name,email,tenantId,tenantName,isActive,isSuperAdmin,lastLoginAt,createdAt
user-id-1,"John Doe",<EMAIL>,tenant-id-1,"Acme Corp",true,false,2024-01-15T10:30:00Z,2024-01-01T09:00:00Z
```

## Security Features

1. **Access Control**
   - Super Admin authentication required
   - Session-based authorization
   - Tenant data isolation

2. **Data Validation**
   - Zod schema validation on all inputs
   - Email format validation
   - Password strength requirements
   - Duplicate email prevention per tenant

3. **Password Security**
   - bcrypt hashing with 12 salt rounds
   - Secure password generation
   - No password exposure in API responses

## File Structure

```
app/
├── admin/users/
│   └── page.tsx                    # Main user management page
├── api/admin/users/
│   ├── route.ts                    # List and create users
│   ├── [id]/route.ts              # Individual user operations
│   ├── [id]/reset-password/route.ts # Password reset
│   ├── import/route.ts            # CSV import
│   └── export/route.ts            # CSV export

components/admin/user-management/
├── users-table.tsx                # Main data table component
├── create-user-dialog.tsx         # Create user form
├── edit-user-dialog.tsx           # Edit user form
├── password-reset-dialog.tsx      # Password reset form
├── import-users-dialog.tsx        # CSV import interface
├── user-details-dialog.tsx        # User details view
└── user-status-badge.tsx          # Status indicator component

lib/
├── validations/user.ts            # Zod validation schemas
└── csv-utils.ts                   # CSV parsing and generation utilities

types/
└── user.ts                       # TypeScript type definitions
```

## Usage Examples

### Creating a New User
1. Navigate to `/admin/users`
2. Click "Add User" button
3. Fill in the form with user details
4. Select tenant organization
5. Set user status and role
6. Click "Create User"

### Importing Users from CSV
1. Navigate to `/admin/users`
2. Click "Import" button
3. Download the CSV template (optional)
4. Upload your CSV file
5. Review validation results
6. Confirm import

### Resetting User Password
1. Find the user in the table
2. Click the actions menu (three dots)
3. Select "Reset Password"
4. Enter new password or generate random one
5. Click "Reset Password"
6. Share the new password securely with the user

## Error Handling

The system includes comprehensive error handling for:
- Invalid file formats during import
- Duplicate email addresses
- Non-existent tenant IDs
- Validation errors
- Network and server errors
- Permission denied scenarios

## Future Enhancements

Potential improvements for future versions:
- Email notifications for password resets
- User profile management for self-service
- Advanced role-based permissions
- User activity logging
- Bulk user operations
- User invitation system
- Two-factor authentication setup
