import { z } from "zod"

export const createUserSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters").max(100, "Password must be less than 100 characters"),
  tenantId: z.string().min(1, "Tenant is required"),
  isActive: z.boolean().default(true),
  isSuperAdmin: z.boolean().default(false),
})

export const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters").optional(),
  email: z.string().email("Invalid email address").optional(),
  tenantId: z.string().min(1, "Tenant is required").optional(),
  isActive: z.boolean().optional(),
  isSuperAdmin: z.boolean().optional(),
})

export const resetPasswordSchema = z.object({
  password: z.string().min(6, "Password must be at least 6 characters").max(100, "Password must be less than 100 characters"),
})

export const importUsersSchema = z.array(
  z.object({
    name: z.string().min(1, "Name is required"),
    email: z.string().email("Invalid email address"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    tenantId: z.string().min(1, "Tenant is required"),
    isActive: z.boolean().default(true),
  })
)

export type CreateUserInput = z.infer<typeof createUserSchema>
export type UpdateUserInput = z.infer<typeof updateUserSchema>
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>
export type ImportUsersInput = z.infer<typeof importUsersSchema>
