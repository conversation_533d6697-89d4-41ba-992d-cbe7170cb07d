import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth()
    if (!(session as any)?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the original role with its permissions
    const originalRole = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        rolePermissions: {
          select: {
            permissionId: true,
          },
        },
      },
    })

    if (!originalRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 })
    }

    // Generate a unique name for the copied role
    let copyName = `${originalRole.name} (Copy)`
    let counter = 1

    while (true) {
      const existingRole = await prisma.role.findUnique({
        where: {
          tenantId_name: {
            tenantId: originalRole.tenantId,
            name: copyName,
          },
        },
      })

      if (!existingRole) {
        break
      }

      counter++
      copyName = `${originalRole.name} (Copy ${counter})`
    }

    // Create the copied role with the same permissions
    const copiedRole = await prisma.role.create({
      data: {
        name: copyName,
        description: originalRole.description ? `${originalRole.description} (Copy)` : undefined,
        tenantId: originalRole.tenantId,
        isSystem: false, // Copied roles are never system roles
        rolePermissions: {
          create: originalRole.rolePermissions.map(rp => ({
            permissionId: rp.permissionId,
          })),
        },
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true,
          },
        },
      },
    })

    return NextResponse.json(copiedRole, { status: 201 })
  } catch (error) {
    console.error('Error copying role:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
