import { prisma } from './prisma'
import { Tenant, User } from '@prisma/client'

export interface TenantContext {
  tenantId: string
  tenant: Tenant
  user?: User
}

/**
 * Get tenant by slug (URL-friendly identifier)
 */
export async function getTenantBySlug(slug: string): Promise<Tenant | null> {
  return await prisma.tenant.findUnique({
    where: { slug, isActive: true }
  })
}

/**
 * Get tenant by domain
 */
export async function getTenantByDomain(domain: string): Promise<Tenant | null> {
  return await prisma.tenant.findUnique({
    where: { domain, isActive: true }
  })
}

/**
 * Get tenant by ID
 */
export async function getTenantById(id: string): Promise<Tenant | null> {
  return await prisma.tenant.findUnique({
    where: { id, isActive: true }
  })
}

/**
 * Create a new tenant with default roles and permissions
 */
export async function createTenant(data: {
  name: string
  slug: string
  domain?: string
  adminEmail: string
  adminName: string
  adminPassword: string
}): Promise<Tenant> {
  const bcrypt = await import('bcryptjs')
  const hashedPassword = await bcrypt.hash(data.adminPassword, 12)

  return await prisma.$transaction(async (tx) => {
    // Create tenant
    const tenant = await tx.tenant.create({
      data: {
        name: data.name,
        slug: data.slug,
        domain: data.domain,
      }
    })

    // Create default roles
    const adminRole = await tx.role.create({
      data: {
        tenantId: tenant.id,
        name: 'Admin',
        description: 'Full system access',
        isSystem: true,
      }
    })

    const managerRole = await tx.role.create({
      data: {
        tenantId: tenant.id,
        name: 'Manager',
        description: 'Management access',
        isSystem: true,
      }
    })

    const employeeRole = await tx.role.create({
      data: {
        tenantId: tenant.id,
        name: 'Employee',
        description: 'Standard employee access',
        isSystem: true,
      }
    })

    // Create default permissions
    const permissions = [
      // User management
      { name: 'users.create', resource: 'users', action: 'create' },
      { name: 'users.read', resource: 'users', action: 'read' },
      { name: 'users.update', resource: 'users', action: 'update' },
      { name: 'users.delete', resource: 'users', action: 'delete' },
      
      // Customer management
      { name: 'customers.create', resource: 'customers', action: 'create' },
      { name: 'customers.read', resource: 'customers', action: 'read' },
      { name: 'customers.update', resource: 'customers', action: 'update' },
      { name: 'customers.delete', resource: 'customers', action: 'delete' },
      
      // Product management
      { name: 'products.create', resource: 'products', action: 'create' },
      { name: 'products.read', resource: 'products', action: 'read' },
      { name: 'products.update', resource: 'products', action: 'update' },
      { name: 'products.delete', resource: 'products', action: 'delete' },
      
      // Order management
      { name: 'orders.create', resource: 'orders', action: 'create' },
      { name: 'orders.read', resource: 'orders', action: 'read' },
      { name: 'orders.update', resource: 'orders', action: 'update' },
      { name: 'orders.delete', resource: 'orders', action: 'delete' },
      
      // Invoice management
      { name: 'invoices.create', resource: 'invoices', action: 'create' },
      { name: 'invoices.read', resource: 'invoices', action: 'read' },
      { name: 'invoices.update', resource: 'invoices', action: 'update' },
      { name: 'invoices.delete', resource: 'invoices', action: 'delete' },
      
      // Payment management
      { name: 'payments.create', resource: 'payments', action: 'create' },
      { name: 'payments.read', resource: 'payments', action: 'read' },
      { name: 'payments.update', resource: 'payments', action: 'update' },
      { name: 'payments.delete', resource: 'payments', action: 'delete' },
    ]

    const createdPermissions = await Promise.all(
      permissions.map(perm => 
        tx.permission.create({
          data: {
            tenantId: tenant.id,
            ...perm
          }
        })
      )
    )

    // Assign all permissions to admin role
    await Promise.all(
      createdPermissions.map(permission =>
        tx.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        })
      )
    )

    // Create admin user
    const adminUser = await tx.user.create({
      data: {
        tenantId: tenant.id,
        email: data.adminEmail,
        name: data.adminName,
        password: hashedPassword,
      }
    })

    // Assign admin role to admin user
    await tx.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    })

    return tenant
  })
}

/**
 * Get user with tenant and roles
 */
export async function getUserWithTenant(userId: string) {
  return await prisma.user.findUnique({
    where: { id: userId },
    include: {
      tenant: true,
      userRoles: {
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      }
    }
  })
}

/**
 * Check if user has permission
 */
export async function userHasPermission(
  userId: string, 
  permissionName: string
): Promise<boolean> {
  const user = await getUserWithTenant(userId)
  if (!user) return false

  const permissions = user.userRoles.flatMap(ur => 
    ur.role.rolePermissions.map(rp => rp.permission.name)
  )

  return permissions.includes(permissionName)
}

/**
 * Tenant-aware Prisma client wrapper
 */
export function createTenantPrisma(tenantId: string) {
  return {
    // Customer operations
    customer: {
      findMany: (args?: any) => prisma.customer.findMany({
        ...args,
        where: { ...args?.where, tenantId }
      }),
      findUnique: (args: any) => prisma.customer.findUnique({
        ...args,
        where: { ...args.where, tenantId }
      }),
      create: (args: any) => prisma.customer.create({
        ...args,
        data: { ...args.data, tenantId }
      }),
      update: (args: any) => prisma.customer.update({
        ...args,
        where: { ...args.where, tenantId }
      }),
      delete: (args: any) => prisma.customer.delete({
        ...args,
        where: { ...args.where, tenantId }
      }),
    },
    
    // Product operations
    product: {
      findMany: (args?: any) => prisma.product.findMany({
        ...args,
        where: { ...args?.where, tenantId }
      }),
      findUnique: (args: any) => prisma.product.findUnique({
        ...args,
        where: { ...args.where, tenantId }
      }),
      create: (args: any) => prisma.product.create({
        ...args,
        data: { ...args.data, tenantId }
      }),
      update: (args: any) => prisma.product.update({
        ...args,
        where: { ...args.where, tenantId }
      }),
      delete: (args: any) => prisma.product.delete({
        ...args,
        where: { ...args.where, tenantId }
      }),
    },
    
    // Order operations
    order: {
      findMany: (args?: any) => prisma.order.findMany({
        ...args,
        where: { ...args?.where, tenantId }
      }),
      findUnique: (args: any) => prisma.order.findUnique({
        ...args,
        where: { ...args.where, tenantId }
      }),
      create: (args: any) => prisma.order.create({
        ...args,
        data: { ...args.data, tenantId }
      }),
      update: (args: any) => prisma.order.update({
        ...args,
        where: { ...args.where, tenantId }
      }),
      delete: (args: any) => prisma.order.delete({
        ...args,
        where: { ...args.where, tenantId }
      }),
    },
    
    // Add more models as needed...
  }
}
