import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Check if user is super admin
async function checkSuperAdmin(session: any) {
  if (!session?.user?.id) {
    return false
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { isSuperAdmin: true }
  })

  return user?.isSuperAdmin || false
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    // Check if tenant exists
    const tenant = await prisma.tenant.findUnique({
      where: { id: params.id },
      select: { id: true, name: true, slug: true }
    })

    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 })
    }

    // Get current date for time-based queries
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    // Get comprehensive statistics
    const [
      totalUsers,
      activeUsers,
      totalCustomers,
      totalSuppliers,
      totalProducts,
      totalOrders,
      totalInvoices,
      totalPayments,
      recentUsers,
      recentOrders,
      recentInvoices,
      orderStats,
      invoiceStats,
      paymentStats
    ] = await Promise.all([
      // User statistics
      prisma.user.count({
        where: { tenantId: params.id }
      }),
      prisma.user.count({
        where: { 
          tenantId: params.id,
          isActive: true,
          lastLoginAt: { gte: thirtyDaysAgo }
        }
      }),
      
      // Entity counts
      prisma.customer.count({
        where: { tenantId: params.id }
      }),
      prisma.supplier.count({
        where: { tenantId: params.id }
      }),
      prisma.product.count({
        where: { tenantId: params.id }
      }),
      prisma.order.count({
        where: { tenantId: params.id }
      }),
      prisma.invoice.count({
        where: { tenantId: params.id }
      }),
      prisma.payment.count({
        where: { tenantId: params.id }
      }),

      // Recent activity
      prisma.user.count({
        where: { 
          tenantId: params.id,
          createdAt: { gte: sevenDaysAgo }
        }
      }),
      prisma.order.count({
        where: { 
          tenantId: params.id,
          createdAt: { gte: sevenDaysAgo }
        }
      }),
      prisma.invoice.count({
        where: { 
          tenantId: params.id,
          createdAt: { gte: sevenDaysAgo }
        }
      }),

      // Order statistics
      prisma.order.aggregate({
        where: { tenantId: params.id },
        _sum: { total: true },
        _avg: { total: true }
      }),

      // Invoice statistics
      prisma.invoice.aggregate({
        where: { tenantId: params.id },
        _sum: { total: true },
        _avg: { total: true }
      }),

      // Payment statistics
      prisma.payment.aggregate({
        where: { tenantId: params.id },
        _sum: { amount: true },
        _avg: { amount: true }
      })
    ])

    // Get order status breakdown
    const orderStatusBreakdown = await prisma.order.groupBy({
      by: ['status'],
      where: { tenantId: params.id },
      _count: { status: true }
    })

    // Get invoice status breakdown
    const invoiceStatusBreakdown = await prisma.invoice.groupBy({
      by: ['status'],
      where: { tenantId: params.id },
      _count: { status: true }
    })

    // Get monthly growth data (last 6 months)
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1)
    const monthlyGrowth = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "createdAt") as month,
        COUNT(CASE WHEN table_name = 'users' THEN 1 END) as users,
        COUNT(CASE WHEN table_name = 'customers' THEN 1 END) as customers,
        COUNT(CASE WHEN table_name = 'orders' THEN 1 END) as orders
      FROM (
        SELECT 'users' as table_name, "createdAt" FROM "users" WHERE "tenantId" = ${params.id} AND "createdAt" >= ${sixMonthsAgo}
        UNION ALL
        SELECT 'customers' as table_name, "createdAt" FROM "customers" WHERE "tenantId" = ${params.id} AND "createdAt" >= ${sixMonthsAgo}
        UNION ALL
        SELECT 'orders' as table_name, "createdAt" FROM "orders" WHERE "tenantId" = ${params.id} AND "createdAt" >= ${sixMonthsAgo}
      ) combined
      GROUP BY DATE_TRUNC('month', "createdAt")
      ORDER BY month
    `

    const stats = {
      tenant,
      overview: {
        totalUsers,
        activeUsers,
        totalCustomers,
        totalSuppliers,
        totalProducts,
        totalOrders,
        totalInvoices,
        totalPayments
      },
      recentActivity: {
        newUsers: recentUsers,
        newOrders: recentOrders,
        newInvoices: recentInvoices
      },
      financial: {
        totalOrderValue: orderStats._sum.total || 0,
        averageOrderValue: orderStats._avg.total || 0,
        totalInvoiceValue: invoiceStats._sum.total || 0,
        averageInvoiceValue: invoiceStats._avg.total || 0,
        totalPayments: paymentStats._sum.amount || 0,
        averagePayment: paymentStats._avg.amount || 0
      },
      breakdowns: {
        orderStatus: orderStatusBreakdown.map(item => ({
          status: item.status,
          count: item._count.status
        })),
        invoiceStatus: invoiceStatusBreakdown.map(item => ({
          status: item.status,
          count: item._count.status
        }))
      },
      growth: monthlyGrowth
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching tenant statistics:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
