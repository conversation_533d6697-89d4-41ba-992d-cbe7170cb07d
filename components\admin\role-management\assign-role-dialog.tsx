"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import { <PERSON>ader2, <PERSON>r<PERSON><PERSON><PERSON>, User, <PERSON> } from 'lucide-react'

const assignRoleSchema = z.object({
  userIds: z.array(z.string()).min(1, 'At least one user must be selected'),
  action: z.enum(['assign', 'revoke']),
})

type AssignRoleInput = z.infer<typeof assignRoleSchema>

interface User {
  id: string
  name?: string
  email: string
  isActive: boolean
  userRoles: {
    role: {
      id: string
      name: string
    }
  }[]
}

interface Role {
  id: string
  name: string
  description?: string
  isSystem: boolean
  tenant: {
    id: string
    name: string
    slug: string
  }
}

interface AssignRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: Role
  onSuccess: () => void
}

export function AssignRoleDialog({ open, onOpenChange, role, onSuccess }: AssignRoleDialogProps) {
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [actionType, setActionType] = useState<'assign' | 'revoke'>('assign')
  const { toast } = useToast()

  const form = useForm<AssignRoleInput>({
    resolver: zodResolver(assignRoleSchema),
    defaultValues: {
      userIds: [],
      action: 'assign',
    },
  })

  const fetchUsers = async () => {
    try {
      const response = await fetch(`/api/admin/users?tenantId=${role.tenant.id}&limit=1000`)
      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }
      const data = await response.json()
      setUsers(data.users || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    if (open) {
      fetchUsers()
      form.reset({
        userIds: [],
        action: 'assign',
      })
      setActionType('assign')
      setSearchTerm('')
    }
  }, [open, form])

  useEffect(() => {
    if (actionType === 'assign') {
      // Show users who don't have this role
      const usersWithoutRole = users.filter(user => 
        !user.userRoles.some(ur => ur.role.id === role.id)
      )
      setFilteredUsers(usersWithoutRole.filter(user =>
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    } else {
      // Show users who have this role
      const usersWithRole = users.filter(user => 
        user.userRoles.some(ur => ur.role.id === role.id)
      )
      setFilteredUsers(usersWithRole.filter(user =>
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    }
  }, [users, role.id, actionType, searchTerm])

  const onSubmit = async (data: AssignRoleInput) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/roles/${role.id}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || `Failed to ${data.action} role`)
      }

      onSuccess()
    } catch (error: any) {
      console.error(`Error ${actionType}ing role:`, error)
      toast({
        title: "Error",
        description: error.message || `Failed to ${actionType} role`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const selectedUserIds = form.watch('userIds') || []

  const toggleUser = (userId: string) => {
    const current = selectedUserIds
    const updated = current.includes(userId)
      ? current.filter(id => id !== userId)
      : [...current, userId]
    form.setValue('userIds', updated)
  }

  const toggleAllUsers = () => {
    const allUserIds = filteredUsers.map(user => user.id)
    const allSelected = allUserIds.every(id => selectedUserIds.includes(id))
    
    if (allSelected) {
      form.setValue('userIds', [])
    } else {
      form.setValue('userIds', allUserIds)
    }
  }

  const handleActionChange = (action: 'assign' | 'revoke') => {
    setActionType(action)
    form.setValue('action', action)
    form.setValue('userIds', [])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Manage Role Assignment: {role.name}
          </DialogTitle>
          <DialogDescription>
            Assign or revoke this role for users in the {role.tenant.name} tenant.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="action"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Action</FormLabel>
                  <Select 
                    value={actionType} 
                    onValueChange={(value: 'assign' | 'revoke') => {
                      handleActionChange(value)
                      field.onChange(value)
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="assign">Assign Role to Users</SelectItem>
                      <SelectItem value="revoke">Revoke Role from Users</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {actionType === 'assign' 
                      ? 'Select users to assign this role to'
                      : 'Select users to revoke this role from'
                    }
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <FormLabel>
                  {actionType === 'assign' ? 'Available Users' : 'Users with Role'} 
                  ({filteredUsers.length})
                </FormLabel>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={toggleAllUsers}
                    disabled={filteredUsers.length === 0}
                  >
                    {filteredUsers.length > 0 && filteredUsers.every(user => selectedUserIds.includes(user.id))
                      ? 'Deselect All'
                      : 'Select All'
                    }
                  </Button>
                </div>
              </div>

              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <FormField
                control={form.control}
                name="userIds"
                render={() => (
                  <FormItem>
                    <ScrollArea className="h-[300px] border rounded-md p-4">
                      {filteredUsers.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          {actionType === 'assign' 
                            ? 'No users available to assign this role to'
                            : 'No users currently have this role'
                          }
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {filteredUsers.map((user) => (
                            <div key={user.id} className="flex items-center space-x-3">
                              <Checkbox
                                checked={selectedUserIds.includes(user.id)}
                                onCheckedChange={() => toggleUser(user.id)}
                              />
                              <div className="flex items-center gap-3 flex-1">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                                  <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium flex items-center gap-2">
                                    {user.name || 'N/A'}
                                    <Badge variant={user.isActive ? "default" : "secondary"} className="text-xs">
                                      {user.isActive ? 'Active' : 'Inactive'}
                                    </Badge>
                                  </div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                  {user.userRoles.length > 0 && (
                                    <div className="flex gap-1 mt-1">
                                      {user.userRoles.slice(0, 3).map((ur) => (
                                        <Badge key={ur.role.id} variant="outline" className="text-xs">
                                          {ur.role.name}
                                        </Badge>
                                      ))}
                                      {user.userRoles.length > 3 && (
                                        <Badge variant="outline" className="text-xs">
                                          +{user.userRoles.length - 3} more
                                        </Badge>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </ScrollArea>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={loading || selectedUserIds.length === 0}
                variant={actionType === 'revoke' ? 'destructive' : 'default'}
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {actionType === 'assign' ? 'Assign Role' : 'Revoke Role'}
                {selectedUserIds.length > 0 && ` (${selectedUserIds.length})`}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
