"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Key, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Shield,
  Edit,
  Trash2,
  Eye,
  Settings
} from 'lucide-react'
import { CreatePermissionDialog } from '@/components/admin/permission-management/create-permission-dialog'
import { EditPermissionDialog } from '@/components/admin/permission-management/edit-permission-dialog'
import { PermissionDetailsDialog } from '@/components/admin/permission-management/permission-details-dialog'
import { useToast } from '@/hooks/use-toast'

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
  createdAt: string
  tenant: {
    id: string
    name: string
    slug: string
  }
  _count: {
    rolePermissions: number
  }
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}

const PERMISSION_CATEGORIES = [
  { value: 'all', label: 'All Categories' },
  { value: 'users', label: 'User Management' },
  { value: 'roles', label: 'Role Management' },
  { value: 'tenants', label: 'Tenant Management' },
  { value: 'customers', label: 'Customer Management' },
  { value: 'suppliers', label: 'Supplier Management' },
  { value: 'products', label: 'Product Management' },
  { value: 'orders', label: 'Order Management' },
  { value: 'invoices', label: 'Invoice Management' },
  { value: 'payments', label: 'Payment Management' },
  { value: 'inventory', label: 'Inventory Management' },
  { value: 'reports', label: 'Reports' },
  { value: 'settings', label: 'Settings' },
]

const ACTION_TYPES = [
  { value: 'all', label: 'All Actions' },
  { value: 'create', label: 'Create' },
  { value: 'read', label: 'Read' },
  { value: 'update', label: 'Update' },
  { value: 'delete', label: 'Delete' },
  { value: 'manage', label: 'Manage' },
]

export default function PermissionsPage() {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState('')
  const [resourceFilter, setResourceFilter] = useState('all')
  const [actionFilter, setActionFilter] = useState('all')
  const [tenantFilter, setTenantFilter] = useState('all')
  const [page, setPage] = useState(1)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  })
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)
  const { toast } = useToast()

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        search,
        ...(resourceFilter !== 'all' && { resource: resourceFilter }),
        ...(actionFilter !== 'all' && { action: actionFilter }),
        ...(tenantFilter !== 'all' && { tenantId: tenantFilter })
      })

      const response = await fetch(`/api/admin/permissions?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch permissions')
      }

      const data = await response.json()
      setPermissions(data.permissions)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error fetching permissions:', error)
      toast({
        title: "Error",
        description: "Failed to fetch permissions",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPermissions()
  }, [page, search, resourceFilter, actionFilter, tenantFilter])

  const handleSearch = (value: string) => {
    setSearch(value)
    setPage(1)
  }

  const handleResourceFilterChange = (value: string) => {
    setResourceFilter(value)
    setPage(1)
  }

  const handleActionFilterChange = (value: string) => {
    setActionFilter(value)
    setPage(1)
  }

  const handleTenantFilterChange = (value: string) => {
    setTenantFilter(value)
    setPage(1)
  }

  const handleEdit = (permission: Permission) => {
    setSelectedPermission(permission)
    setEditDialogOpen(true)
  }

  const handleViewDetails = (permission: Permission) => {
    setSelectedPermission(permission)
    setDetailsDialogOpen(true)
  }

  const handleDelete = async (permission: Permission) => {
    if (!confirm(`Are you sure you want to delete permission "${permission.name}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/permissions/${permission.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete permission')
      }

      toast({
        title: "Success",
        description: "Permission deleted successfully",
      })
      
      fetchPermissions()
    } catch (error: any) {
      console.error('Error deleting permission:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete permission",
        variant: "destructive",
      })
    }
  }

  const handlePermissionCreated = () => {
    setCreateDialogOpen(false)
    fetchPermissions()
    toast({
      title: "Success",
      description: "Permission created successfully",
    })
  }

  const handlePermissionUpdated = () => {
    setEditDialogOpen(false)
    setSelectedPermission(null)
    fetchPermissions()
    toast({
      title: "Success",
      description: "Permission updated successfully",
    })
  }

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'read': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'update': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'delete': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'manage': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Permission Management</h1>
          <p className="text-muted-foreground">
            Manage fine-grained permissions for resources and actions
          </p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Permission
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search permissions..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={resourceFilter} onValueChange={handleResourceFilterChange}>
              <SelectTrigger className="w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by resource" />
              </SelectTrigger>
              <SelectContent>
                {PERMISSION_CATEGORIES.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={actionFilter} onValueChange={handleActionFilterChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                {ACTION_TYPES.map((action) => (
                  <SelectItem key={action.value} value={action.value}>
                    {action.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions ({pagination.total})</CardTitle>
          <CardDescription>
            All permissions in your system organized by resource and action
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Permission</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Roles</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 3 }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-[150px]" />
                          <Skeleton className="h-3 w-[100px]" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-[80px] rounded-full" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-[70px] rounded-full" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[200px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[60px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[80px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-8 w-8 rounded" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : permissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Key className="h-12 w-12 text-muted-foreground" />
                        <div className="text-muted-foreground">No permissions found</div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  permissions.map((permission) => (
                    <TableRow key={permission.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium flex items-center gap-2">
                            <Key className="h-4 w-4 text-blue-600" />
                            {permission.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {permission.tenant.name}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {permission.resource}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="secondary"
                          className={`capitalize ${getActionColor(permission.action)}`}
                        >
                          {permission.action}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[200px] truncate">
                          {permission.description || 'No description'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Shield className="h-3 w-3 text-muted-foreground" />
                          {permission._count.rolePermissions}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {new Date(permission.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewDetails(permission)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleEdit(permission)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDelete(permission)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((page - 1) * pagination.limit) + 1} to {Math.min(page * pagination.limit, pagination.total)} of {pagination.total} permissions
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Dialogs */}
      <CreatePermissionDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={handlePermissionCreated}
      />

      {selectedPermission && (
        <>
          <EditPermissionDialog
            open={editDialogOpen}
            onOpenChange={setEditDialogOpen}
            permission={selectedPermission}
            onSuccess={handlePermissionUpdated}
          />
          <PermissionDetailsDialog
            open={detailsDialogOpen}
            onOpenChange={setDetailsDialogOpen}
            permission={selectedPermission}
          />
        </>
      )}
    </div>
  )
}
