import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { parseCSV, validateUserCSVData } from '@/lib/csv-utils'
import bcrypt from 'bcryptjs'

// Check if user is super admin
async function checkSuperAdmin(session: any) {
  if (!session?.user?.id) {
    return false
  }

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { isSuperAdmin: true }
  })

  return user?.isSuperAdmin || false
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const isSuperAdmin = await checkSuperAdmin(session)
    if (!isSuperAdmin) {
      return NextResponse.json({ error: 'Super admin access required' }, { status: 403 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!file.name.endsWith('.csv')) {
      return NextResponse.json({ error: 'File must be a CSV' }, { status: 400 })
    }

    const csvContent = await file.text()
    
    // Parse and validate CSV data
    const rawData = parseCSV(csvContent)
    const validatedData = validateUserCSVData(rawData)

    // Check for duplicate emails within the import
    const emails = validatedData.map(user => user.email.toLowerCase())
    const duplicateEmails = emails.filter((email, index) => emails.indexOf(email) !== index)
    
    if (duplicateEmails.length > 0) {
      return NextResponse.json({
        error: 'Duplicate emails found in import file',
        details: duplicateEmails
      }, { status: 400 })
    }

    // Check for existing users
    const existingUsers = await prisma.user.findMany({
      where: {
        OR: validatedData.map(user => ({
          email: user.email,
          tenantId: user.tenantId
        }))
      },
      select: { email: true, tenantId: true }
    })

    if (existingUsers.length > 0) {
      const conflicts = existingUsers.map(user => `${user.email} (tenant: ${user.tenantId})`)
      return NextResponse.json({
        error: 'Some users already exist',
        details: conflicts
      }, { status: 400 })
    }

    // Verify all tenants exist
    const tenantIds = [...new Set(validatedData.map(user => user.tenantId))]
    const existingTenants = await prisma.tenant.findMany({
      where: { id: { in: tenantIds } },
      select: { id: true }
    })

    const missingTenants = tenantIds.filter(
      tenantId => !existingTenants.find(tenant => tenant.id === tenantId)
    )

    if (missingTenants.length > 0) {
      return NextResponse.json({
        error: 'Some tenants do not exist',
        details: missingTenants
      }, { status: 400 })
    }

    // Hash passwords and create users
    const usersToCreate = await Promise.all(
      validatedData.map(async (user) => ({
        ...user,
        password: await bcrypt.hash(user.password, 12),
        email: user.email.toLowerCase(),
      }))
    )

    const createdUsers = await prisma.user.createMany({
      data: usersToCreate
    })

    return NextResponse.json({
      message: `Successfully imported ${createdUsers.count} users`,
      count: createdUsers.count
    })
  } catch (error) {
    console.error('Error importing users:', error)
    
    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
