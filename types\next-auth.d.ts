import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
    }
    tenantId?: string
    tenantSlug?: string
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    tenantId?: string
    tenantSlug?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string
    tenantId?: string
    tenantSlug?: string
  }
}
