"use client"

import { useSession, signOut } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AppLayout } from "@/components/layout"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from "@/components/ui/chart"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MoreHorizontal, TrendingUp, TrendingDown, Building2 } from "lucide-react"
import { Area, AreaChart, XAxis, YAxis } from "recharts"

// Sample data for the charts
const chartData = [
  { month: "Jan", value: 180, profit: 120 },
  { month: "Feb", value: 200, profit: 140 },
  { month: "Mar", value: 170, profit: 110 },
  { month: "Apr", value: 190, profit: 130 },
  { month: "May", value: 160, profit: 100 },
  { month: "Jun", value: 220, profit: 160 },
  { month: "Jul", value: 240, profit: 180 },
  { month: "Aug", value: 230, profit: 170 },
  { month: "Sep", value: 250, profit: 190 },
  { month: "Oct", value: 260, profit: 200 },
  { month: "Nov", value: 240, profit: 180 },
  { month: "Dec", value: 280, profit: 220 },
]

const chartConfig = {
  value: {
    label: "Value",
    color: "hsl(var(--chart-1))",
  },
  profit: {
    label: "Profit",
    color: "hsl(var(--chart-2))",
  },
}

export function DashboardClient() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading

    if (!session) {
      router.push("/signin")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null // Will redirect
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/signin" })
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Tenant Info Header */}
        {session?.tenantSlug && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <div>
                <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                  Multi-Tenant ERP Dashboard
                </h2>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Currently viewing data for organization: <span className="font-medium">{session.tenantSlug}</span>
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Top Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
          {/* Active Deal Card */}
          <Card className="p-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-muted-foreground">Active Deal</p>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-3 w-3" />
                  <span className="text-xs font-medium">+20%</span>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold">$120,369</p>
                <p className="text-xs text-muted-foreground">last month</p>
              </div>
            </div>
          </Card>

          {/* Revenue Total Card */}
          <Card className="p-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-muted-foreground">Revenue Total</p>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-3 w-3" />
                  <span className="text-xs font-medium">+9.0%</span>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold">$234,210</p>
                <p className="text-xs text-muted-foreground">last month</p>
              </div>
            </div>
          </Card>

          {/* Closed Deals Card */}
          <Card className="p-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-muted-foreground">Closed Deals</p>
                <div className="flex items-center gap-1 text-red-600">
                  <TrendingDown className="h-3 w-3" />
                  <span className="text-xs font-medium">-4.5%</span>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold">874</p>
                <p className="text-xs text-muted-foreground">last month</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Bottom Section with Statistics and Estimated Revenue */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
          {/* Statistics Chart - Takes 2 columns on large screens */}
          <Card className="lg:col-span-2 p-6">
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <h3 className="text-lg font-semibold">Statistics</h3>
                  <p className="text-sm text-muted-foreground">Target you've set for each month</p>
                </div>
                <Tabs defaultValue="monthly" className="w-auto">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="monthly">Monthly</TabsTrigger>
                    <TabsTrigger value="quarterly">Quarterly</TabsTrigger>
                    <TabsTrigger value="annually">Annually</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <p className="text-xl font-bold">$212,142.12</p>
                    <Badge variant="secondary" className="text-green-600 bg-green-50">+23.2%</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">Avg. Yearly Profit</p>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <p className="text-xl font-bold">$30,321.23</p>
                    <Badge variant="secondary" className="text-red-600 bg-red-50">-12.3%</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">Avg. Yearly Profit</p>
                </div>
              </div>

              <div className="h-[200px] sm:h-[250px]">
                <ChartContainer config={chartConfig}>
                  <AreaChart data={chartData}>
                    <defs>
                      <linearGradient id="fillValue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="hsl(var(--chart-1))" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="hsl(var(--chart-1))" stopOpacity={0.1}/>
                      </linearGradient>
                      <linearGradient id="fillProfit" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="hsl(var(--chart-2))" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="hsl(var(--chart-2))" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                      className="text-muted-foreground"
                    />
                    <YAxis hide />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      dataKey="profit"
                      type="monotone"
                      fill="url(#fillProfit)"
                      fillOpacity={0.4}
                      stroke="hsl(var(--chart-2))"
                      strokeWidth={2}
                    />
                    <Area
                      dataKey="value"
                      type="monotone"
                      fill="url(#fillValue)"
                      fillOpacity={0.4}
                      stroke="hsl(var(--chart-1))"
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ChartContainer>
              </div>
            </div>
          </Card>

          {/* Estimated Revenue Card */}
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Estimated Revenue</h3>
                  <p className="text-sm text-muted-foreground">Target you've set for each month</p>
                </div>
                <MoreHorizontal className="h-5 w-5 text-muted-foreground" />
              </div>

              {/* Circular Progress */}
              <div className="flex flex-col items-center space-y-4">
                <div className="relative w-32 h-32">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="currentColor"
                      strokeWidth="8"
                      fill="none"
                      className="text-muted-foreground/20"
                    />
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      stroke="hsl(var(--chart-1))"
                      strokeWidth="8"
                      fill="none"
                      strokeDasharray={`${2 * Math.PI * 50}`}
                      strokeDashoffset={`${2 * Math.PI * 50 * (1 - 0.75)}`}
                      className="transition-all duration-300"
                      strokeLinecap="round"
                    />
                  </svg>
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <span className="text-xs text-muted-foreground">June Goals</span>
                    <span className="text-2xl font-bold">$90</span>
                  </div>
                </div>
              </div>

              {/* Progress Items */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Marketing</span>
                    <span className="text-sm font-medium">85%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">$30,569.00</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Sales</span>
                    <span className="text-sm font-medium">55%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold">$20,486.00</span>
                  </div>
                  <Progress value={55} className="h-2" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
