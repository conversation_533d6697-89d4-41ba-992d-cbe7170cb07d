import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle } from 'lucide-react'

interface UserStatusBadgeProps {
  isActive: boolean
}

export function UserStatusBadge({ isActive }: UserStatusBadgeProps) {
  if (isActive) {
    return (
      <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    )
  }

  return (
    <Badge variant="secondary" className="bg-red-100 text-red-800 hover:bg-red-100">
      <XCircle className="h-3 w-3 mr-1" />
      Inactive
    </Badge>
  )
}
