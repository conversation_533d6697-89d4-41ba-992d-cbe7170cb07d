import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create default tenant
  const defaultTenant = await prisma.tenant.upsert({
    where: { slug: 'default' },
    update: {},
    create: {
      name: 'Default Organization',
      slug: 'default',
      settings: {
        currency: 'USD',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        theme: 'light'
      }
    }
  })

  console.log('✅ Created default tenant:', defaultTenant.name)

  // Create default roles
  const adminRole = await prisma.role.upsert({
    where: { tenantId_name: { tenantId: defaultTenant.id, name: 'Admin' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      name: 'Admin',
      description: 'Full system access',
      isSystem: true,
    }
  })

  const managerRole = await prisma.role.upsert({
    where: { tenantId_name: { tenantId: defaultTenant.id, name: 'Manager' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      name: 'Manager',
      description: 'Management access',
      isSystem: true,
    }
  })

  const employeeRole = await prisma.role.upsert({
    where: { tenantId_name: { tenantId: defaultTenant.id, name: 'Employee' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      name: 'Employee',
      description: 'Standard employee access',
      isSystem: true,
    }
  })

  console.log('✅ Created default roles')

  // Create default permissions
  const permissions = [
    // User management
    { name: 'users.create', resource: 'users', action: 'create', description: 'Create users' },
    { name: 'users.read', resource: 'users', action: 'read', description: 'View users' },
    { name: 'users.update', resource: 'users', action: 'update', description: 'Update users' },
    { name: 'users.delete', resource: 'users', action: 'delete', description: 'Delete users' },
    
    // Customer management
    { name: 'customers.create', resource: 'customers', action: 'create', description: 'Create customers' },
    { name: 'customers.read', resource: 'customers', action: 'read', description: 'View customers' },
    { name: 'customers.update', resource: 'customers', action: 'update', description: 'Update customers' },
    { name: 'customers.delete', resource: 'customers', action: 'delete', description: 'Delete customers' },
    
    // Product management
    { name: 'products.create', resource: 'products', action: 'create', description: 'Create products' },
    { name: 'products.read', resource: 'products', action: 'read', description: 'View products' },
    { name: 'products.update', resource: 'products', action: 'update', description: 'Update products' },
    { name: 'products.delete', resource: 'products', action: 'delete', description: 'Delete products' },
    
    // Order management
    { name: 'orders.create', resource: 'orders', action: 'create', description: 'Create orders' },
    { name: 'orders.read', resource: 'orders', action: 'read', description: 'View orders' },
    { name: 'orders.update', resource: 'orders', action: 'update', description: 'Update orders' },
    { name: 'orders.delete', resource: 'orders', action: 'delete', description: 'Delete orders' },
    
    // Invoice management
    { name: 'invoices.create', resource: 'invoices', action: 'create', description: 'Create invoices' },
    { name: 'invoices.read', resource: 'invoices', action: 'read', description: 'View invoices' },
    { name: 'invoices.update', resource: 'invoices', action: 'update', description: 'Update invoices' },
    { name: 'invoices.delete', resource: 'invoices', action: 'delete', description: 'Delete invoices' },
    
    // Payment management
    { name: 'payments.create', resource: 'payments', action: 'create', description: 'Create payments' },
    { name: 'payments.read', resource: 'payments', action: 'read', description: 'View payments' },
    { name: 'payments.update', resource: 'payments', action: 'update', description: 'Update payments' },
    { name: 'payments.delete', resource: 'payments', action: 'delete', description: 'Delete payments' },
  ]

  const createdPermissions = []
  for (const perm of permissions) {
    const permission = await prisma.permission.upsert({
      where: { tenantId_name: { tenantId: defaultTenant.id, name: perm.name } },
      update: {},
      create: {
        tenantId: defaultTenant.id,
        ...perm
      }
    })
    createdPermissions.push(permission)
  }

  console.log('✅ Created default permissions')

  // Assign all permissions to admin role
  for (const permission of createdPermissions) {
    await prisma.rolePermission.upsert({
      where: { roleId_permissionId: { roleId: adminRole.id, permissionId: permission.id } },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id
      }
    })
  }

  // Assign read permissions to employee role
  const readPermissions = createdPermissions.filter(p => p.action === 'read')
  for (const permission of readPermissions) {
    await prisma.rolePermission.upsert({
      where: { roleId_permissionId: { roleId: employeeRole.id, permissionId: permission.id } },
      update: {},
      create: {
        roleId: employeeRole.id,
        permissionId: permission.id
      }
    })
  }

  console.log('✅ Assigned permissions to roles')

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { tenantId_email: { tenantId: defaultTenant.id, email: '<EMAIL>' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      email: '<EMAIL>',
      name: 'System Administrator',
      password: hashedPassword,
    }
  })

  // Assign admin role to admin user
  await prisma.userRole.upsert({
    where: { userId_roleId: { userId: adminUser.id, roleId: adminRole.id } },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id
    }
  })

  console.log('✅ Created default admin user')

  // Create super admin user (not tied to any tenant)
  const superAdminPassword = await bcrypt.hash('superadmin123', 12)

  // Check if super admin already exists
  const existingSuperAdmin = await prisma.user.findFirst({
    where: {
      email: '<EMAIL>',
      isSuperAdmin: true
    }
  })

  if (!existingSuperAdmin) {
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Super Administrator',
        password: superAdminPassword,
        isSuperAdmin: true,
        tenantId: null, // Super admin is not tied to any tenant
      }
    })
  }

  console.log('✅ Created super admin user')

  // Create sample categories
  const categories = [
    { name: 'Electronics', description: 'Electronic devices and accessories' },
    { name: 'Clothing', description: 'Apparel and fashion items' },
    { name: 'Books', description: 'Books and educational materials' },
    { name: 'Home & Garden', description: 'Home improvement and garden supplies' },
  ]

  for (const cat of categories) {
    await prisma.category.upsert({
      where: { tenantId_name: { tenantId: defaultTenant.id, name: cat.name } },
      update: {},
      create: {
        tenantId: defaultTenant.id,
        ...cat
      }
    })
  }

  console.log('✅ Created sample categories')

  // Create sample supplier
  const supplier = await prisma.supplier.upsert({
    where: { tenantId_email: { tenantId: defaultTenant.id, email: '<EMAIL>' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      name: 'Sample Supplier Inc.',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Business St',
      city: 'Business City',
      state: 'BC',
      zipCode: '12345',
      country: 'USA',
    }
  })

  console.log('✅ Created sample supplier')

  // Create sample customer
  const customer = await prisma.customer.upsert({
    where: { tenantId_email: { tenantId: defaultTenant.id, email: '<EMAIL>' } },
    update: {},
    create: {
      tenantId: defaultTenant.id,
      name: 'Sample Customer',
      email: '<EMAIL>',
      phone: '******-0456',
      address: '456 Customer Ave',
      city: 'Customer City',
      state: 'CC',
      zipCode: '67890',
      country: 'USA',
    }
  })

  console.log('✅ Created sample customer')

  console.log('🎉 Database seeding completed!')
  console.log('')
  console.log('Default login credentials:')
  console.log('Email: <EMAIL>')
  console.log('Password: admin123')
  console.log('Tenant: default')
  console.log('')
  console.log('Super Admin credentials:')
  console.log('Email: <EMAIL>')
  console.log('Password: superadmin123')
  console.log('Access: /admin')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
