"use client"

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Shield, 
  Key, 
  Users, 
  Calendar,
  Building2,
  User
} from 'lucide-react'

interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
}

interface User {
  id: string
  name?: string
  email: string
  isActive: boolean
}

interface Role {
  id: string
  name: string
  description?: string
  isSystem: boolean
  createdAt: string
  updatedAt: string
  tenant: {
    id: string
    name: string
    slug: string
  }
  _count: {
    userRoles: number
    rolePermissions: number
  }
}

interface RoleDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: Role
}

interface RoleDetails {
  role: Role & {
    rolePermissions: {
      permission: Permission
    }[]
    userRoles: {
      user: User
    }[]
  }
}

export function RoleDetailsDialog({ open, onOpenChange, role }: RoleDetailsDialogProps) {
  const [loading, setLoading] = useState(true)
  const [roleDetails, setRoleDetails] = useState<RoleDetails | null>(null)

  const fetchRoleDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/roles/${role.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch role details')
      }
      const data = await response.json()
      setRoleDetails(data)
    } catch (error) {
      console.error('Error fetching role details:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchRoleDetails()
    }
  }, [open, role.id])

  const getActionColor = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'read': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'update': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'delete': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'manage': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const groupedPermissions = roleDetails?.role.rolePermissions.reduce((acc, rp) => {
    const permission = rp.permission
    if (!acc[permission.resource]) {
      acc[permission.resource] = []
    }
    acc[permission.resource].push(permission)
    return acc
  }, {} as Record<string, Permission[]>) || {}

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Role Details: {role.name}
            {role.isSystem && (
              <Badge variant="default" className="ml-2">System</Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Detailed information about the role, its permissions, and assigned users.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Role Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Role Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <div className="font-medium">{role.name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type</label>
                  <div>
                    <Badge variant={role.isSystem ? "default" : "secondary"}>
                      {role.isSystem ? 'System' : 'Custom'}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Description</label>
                <div className="text-sm">
                  {role.description || 'No description provided'}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Building2 className="h-3 w-3" />
                    Tenant
                  </label>
                  <div>
                    <div className="font-medium">{role.tenant.name}</div>
                    <div className="text-sm text-muted-foreground">{role.tenant.slug}</div>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Created
                  </label>
                  <div className="text-sm">
                    {new Date(role.createdAt).toLocaleDateString()} at{' '}
                    {new Date(role.createdAt).toLocaleTimeString()}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    Assigned Users
                  </label>
                  <div className="font-medium">{role._count.userRoles}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Key className="h-3 w-3" />
                    Permissions
                  </label>
                  <div className="font-medium">{role._count.rolePermissions}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Permissions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Permissions ({role._count.rolePermissions})
              </CardTitle>
              <CardDescription>
                All permissions assigned to this role, grouped by resource.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-6 w-[120px]" />
                      <div className="ml-4 space-y-2">
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-4 w-[180px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : Object.keys(groupedPermissions).length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No permissions assigned to this role
                </div>
              ) : (
                <ScrollArea className="h-[200px]">
                  <div className="space-y-4">
                    {Object.entries(groupedPermissions).map(([resource, permissions]) => (
                      <div key={resource} className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="capitalize font-medium">
                            {resource}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            ({permissions.length} permissions)
                          </span>
                        </div>
                        <div className="ml-4 grid grid-cols-2 gap-2">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="flex items-center gap-2">
                              <Badge 
                                variant="secondary" 
                                className={`text-xs ${getActionColor(permission.action)}`}
                              >
                                {permission.action}
                              </Badge>
                              <span className="text-sm">{permission.name}</span>
                            </div>
                          ))}
                        </div>
                        {resource !== Object.keys(groupedPermissions)[Object.keys(groupedPermissions).length - 1] && (
                          <Separator className="mt-4" />
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {/* Assigned Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Assigned Users ({role._count.userRoles})
              </CardTitle>
              <CardDescription>
                Users who have been assigned this role.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-[150px]" />
                        <Skeleton className="h-3 w-[120px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : !roleDetails?.role.userRoles.length ? (
                <div className="text-center py-8 text-muted-foreground">
                  No users assigned to this role
                </div>
              ) : (
                <ScrollArea className="h-[150px]">
                  <div className="space-y-3">
                    {roleDetails.role.userRoles.map(({ user }) => (
                      <div key={user.id} className="flex items-center gap-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                          <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {user.name || 'N/A'}
                            <Badge variant={user.isActive ? "default" : "secondary"} className="text-xs">
                              {user.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
