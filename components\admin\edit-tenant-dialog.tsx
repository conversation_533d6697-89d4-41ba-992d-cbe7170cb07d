"use client"

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Loader2 } from 'lucide-react'

const editTenantSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  domain: z.string().optional(),
  logo: z.string().optional(),
  isActive: z.boolean(),
  maxUsers: z.number().min(1).optional(),
  maxStorage: z.number().min(1).optional(),
  plan: z.enum(['basic', 'premium', 'enterprise']),
  billingEmail: z.string().email().optional().or(z.literal('')),
  subscriptionStatus: z.enum(['active', 'suspended', 'cancelled']),
})

type EditTenantFormData = z.infer<typeof editTenantSchema>

interface Tenant {
  id: string
  name: string
  slug: string
  domain?: string
  logo?: string
  isActive: boolean
  plan: string
  maxUsers?: number
  maxStorage?: number
  subscriptionStatus: string
  billingEmail?: string
  createdAt: string
  updatedAt: string
  _count: {
    users: number
    customers: number
    orders: number
  }
}

interface EditTenantDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tenant: Tenant
  onSuccess: () => void
}

export function EditTenantDialog({ open, onOpenChange, tenant, onSuccess }: EditTenantDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<EditTenantFormData>({
    resolver: zodResolver(editTenantSchema),
    defaultValues: {
      name: '',
      domain: '',
      logo: '',
      isActive: true,
      maxUsers: 100,
      maxStorage: 1000,
      plan: 'basic',
      billingEmail: '',
      subscriptionStatus: 'active',
    },
  })

  // Update form values when tenant changes
  useEffect(() => {
    if (tenant) {
      form.reset({
        name: tenant.name,
        domain: tenant.domain || '',
        logo: tenant.logo || '',
        isActive: tenant.isActive,
        maxUsers: tenant.maxUsers || 100,
        maxStorage: tenant.maxStorage || 1000,
        plan: tenant.plan as 'basic' | 'premium' | 'enterprise',
        billingEmail: tenant.billingEmail || '',
        subscriptionStatus: tenant.subscriptionStatus as 'active' | 'suspended' | 'cancelled',
      })
    }
  }, [tenant, form])

  const onSubmit = async (data: EditTenantFormData) => {
    try {
      setIsLoading(true)

      // Clean up empty optional fields
      const cleanData = {
        ...data,
        domain: data.domain || undefined,
        logo: data.logo || undefined,
        billingEmail: data.billingEmail || undefined,
        maxUsers: data.maxUsers || undefined,
        maxStorage: data.maxStorage || undefined,
      }

      const response = await fetch(`/api/admin/tenants/${tenant.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update tenant')
      }

      onSuccess()
    } catch (error: any) {
      console.error('Error updating tenant:', error)
      form.setError('root', {
        message: error.message || 'Failed to update tenant'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Tenant: {tenant?.name}</DialogTitle>
          <DialogDescription>
            Update tenant organization settings and configuration.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Acme Corporation" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="domain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Domain</FormLabel>
                    <FormControl>
                      <Input placeholder="acme.example.com" {...field} />
                    </FormControl>
                    <FormDescription>
                      Custom domain for this tenant (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo URL</FormLabel>
                    <FormControl>
                      <Input placeholder="https://example.com/logo.png" {...field} />
                    </FormControl>
                    <FormDescription>
                      URL to the organization's logo (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                      <FormDescription>
                        Enable or disable this tenant organization
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Subscription & Limits */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Subscription & Limits</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="plan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a plan" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="basic">Basic</SelectItem>
                          <SelectItem value="premium">Premium</SelectItem>
                          <SelectItem value="enterprise">Enterprise</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subscriptionStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subscription Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="suspended">Suspended</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="maxUsers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Users</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="100"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxStorage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Storage (MB)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="1000"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="billingEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Billing Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormDescription>
                      Email address for billing notifications (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Tenant Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Tenant Information</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Slug:</span> {tenant?.slug}
                </div>
                <div>
                  <span className="font-medium">Created:</span> {tenant ? new Date(tenant.createdAt).toLocaleDateString() : ''}
                </div>
                <div>
                  <span className="font-medium">Users:</span> {tenant?._count.users || 0}
                </div>
                <div>
                  <span className="font-medium">Customers:</span> {tenant?._count.customers || 0}
                </div>
              </div>
            </div>

            {form.formState.errors.root && (
              <div className="text-sm text-red-600">
                {form.formState.errors.root.message}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Update Tenant
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
