import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updatePermissionSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  resource: z.string().min(1).optional(),
  action: z.string().min(1).optional(),
  description: z.string().optional(),
})

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const permission = await prisma.permission.findUnique({
      where: { id: params.id },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        rolePermissions: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
                isSystem: true,
              },
            },
          },
        },
        _count: {
          select: {
            rolePermissions: true,
          },
        },
      },
    })

    if (!permission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 })
    }

    return NextResponse.json({ permission })
  } catch (error) {
    console.error('Error fetching permission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, resource, action, description } = updatePermissionSchema.parse(body)

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id: params.id },
    })

    if (!existingPermission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 })
    }

    // Check if name is being changed and if it conflicts
    if (name && name !== existingPermission.name) {
      const nameConflict = await prisma.permission.findUnique({
        where: {
          tenantId_name: {
            tenantId: existingPermission.tenantId,
            name,
          },
        },
      })

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Permission name already exists in this tenant' },
          { status: 400 }
        )
      }
    }

    // Update permission
    const permission = await prisma.permission.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(resource && { resource }),
        ...(action && { action }),
        description,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            rolePermissions: true,
          },
        },
      },
    })

    return NextResponse.json(permission)
  } catch (error) {
    console.error('Error updating permission:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if permission exists
    const permission = await prisma.permission.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            rolePermissions: true,
          },
        },
      },
    })

    if (!permission) {
      return NextResponse.json({ error: 'Permission not found' }, { status: 404 })
    }

    // Don't allow deleting permissions that are assigned to roles
    if (permission._count.rolePermissions > 0) {
      return NextResponse.json(
        { error: 'Cannot delete permission that is assigned to roles' },
        { status: 400 }
      )
    }

    // Delete permission
    await prisma.permission.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: 'Permission deleted successfully' })
  } catch (error) {
    console.error('Error deleting permission:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
